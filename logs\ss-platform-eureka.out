[2m2025-07-29 09:32:21.545[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-29 09:32:22.595[0;39m [dev] [33m WARN[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-29 09:32:22.724[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-29 09:32:23.141[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-29 09:32:23.222[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 1660 ms
[2m2025-07-29 09:32:23.297[0;39m [dev] [33m WARN[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-29 09:32:23.298[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-29 09:32:23.310[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@44cffc25
[2m2025-07-29 09:32:24.190[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-29 09:32:24.192[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-29 09:32:24.324[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-29 09:32:24.325[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-29 09:32:24.850[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4cb24e2, org.springframework.security.web.context.SecurityContextPersistenceFilter@6a6da47a, org.springframework.security.web.header.HeaderWriterFilter@4a660b34, org.springframework.security.web.authentication.logout.LogoutFilter@1a7163e3, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@6b6def36, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7f642bf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@49ced9c7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7be38eba, org.springframework.security.web.session.SessionManagementFilter@ad0bb4e, org.springframework.security.web.access.ExceptionTranslationFilter@19705650, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@35e2b89f]
[2m2025-07-29 09:32:24.860[0;39m [dev] [33m WARN[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-29 09:32:24.861[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-29 09:32:24.991[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-29 09:32:25.724[0;39m [dev] [33m WARN[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-29 09:32:25.847[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-29 09:32:25.883[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-29 09:32:25.883[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-29 09:32:25.893[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753752745891 with initial instances count: 0
[2m2025-07-29 09:32:25.932[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-29 09:32:25.933[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-29 09:32:26.345[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-29 09:32:26.345[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-29 09:32:26.345[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-29 09:32:26.345[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-29 09:32:26.425[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-29 09:32:26.433[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-29 09:32:26.434[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-29 09:32:26.444[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-29 09:32:26.520[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-29 09:32:26.522[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-29 09:32:26.522[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-29 09:32:26.523[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-29 09:32:26.540[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-29 09:32:26.541[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-29 09:32:26.541[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-29 09:32:26.541[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-29 09:32:26.541[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-29 09:32:26.547[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[      Thread-25][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-29 09:32:26.562[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-29 09:32:26.564[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-29 09:32:27.006[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 6.717 seconds (JVM running for 8.938)
[2m2025-07-29 09:32:28.252[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[4)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-29 09:32:28.259[0;39m [dev] [32m INFO[0;39m [35m43928[0;39m [2m---[0;39m [2m[4)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 7 ms
