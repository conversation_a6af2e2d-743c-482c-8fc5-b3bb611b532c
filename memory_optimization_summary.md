# AtrBussLrcTotService 内存优化总结

## 优化背景
原有的 `collectionPrePolicyInfoDetailed()` 方法将所有上期详细数据（包含sectionoCode和reinsurerCode的6维度数据）一次性加载到内存中的 `preToLrcUlsDetailed` 索引，在大数据量情况下会导致OOM问题。

## 优化方案
参考 `AtrBussLrcToxService` 中的分片处理方案，将内存索引改为分片+按需查询的方式。

## 核心改动

### 1. 数据库层面
- **新增临时分区表**: `atr_temp_to_lrc_t_ul_r_pre`
- **新增DAO方法**:
  - `truncatePreDetailedTempTable()`: 清空临时表
  - `insertPreDetailedTempData()`: 分片插入历史数据
  - `getPreDetailedDataByKey()`: 按6维度键查询单条数据

### 2. Service层面
- **添加分片配置**: `PARTITION_SIZE = 50`
- **重构数据收集方法**: 
  - 删除 `collectionPrePolicyInfoDetailed()` 
  - 新增 `partitionPreDetailedData()` 分片处理
- **优化查询方式**:
  - 删除 `preToLrcUlsDetailed` 内存索引
  - 新增 `getPreDetailedDataOnDemand()` 按需查询

### 3. 核心逻辑变更
**原逻辑**:
```java
// 一次性加载所有历史数据到内存
List<AtrBussToLrcTUlR> atrBussToLrcTUlRs = atrBussLrcToDao.listPreBussToLrcTUlRDetailed(commonParamMap);
atrBussToLrcTUlRs.forEach(item -> {
    List<?> detailedKey = createDetailedUnitKey(item);
    preToLrcUlsDetailed.add(detailedKey, item);
});

// 从内存索引获取数据
AtrBussToLrcTUlR preToLrcUlR = preToLrcUlsDetailed.one(detailedKey);
```

**新逻辑**:
```java
// 分片处理历史数据
partitionPreDetailedData(); // 将数据分片存储到临时表

// 按需查询单条数据
AtrBussToLrcTUlR preToLrcUlR = getPreDetailedDataOnDemand(icu);
```

## 性能优化效果

### 内存使用
- **优化前**: 所有历史详细数据常驻内存，数据量大时易OOM
- **优化后**: 只在临时表中存储，内存使用大幅降低

### 查询性能
- **优化前**: 内存查询，速度快但占用内存大
- **优化后**: 按需SQL查询，内存友好，查询性能可通过索引优化

### 数据处理方式
- **优化前**: 批量加载 → 内存索引 → 批量处理
- **优化后**: 分片存储 → 按需查询 → 逐条处理

## 业务逻辑保证
1. **6维度匹配**: 保持原有的6维度键匹配逻辑不变
2. **数据完整性**: 通过SQL查询确保数据的准确性
3. **计算逻辑**: `calcIcp()` 方法的业务计算逻辑完全不变

## 风险控制
1. **SQL性能**: 通过在临时表上建立适当索引来保证查询性能
2. **数据一致性**: 使用事务控制确保分片操作的原子性
3. **错误处理**: 添加完善的异常处理和日志记录

## 部署注意事项
1. **数据库表创建**: 需要创建 `atr_temp_to_lrc_t_ul_r_pre` 临时表
2. **索引优化**: 建议在临时表的查询字段上建立复合索引
3. **监控指标**: 关注内存使用情况和SQL查询性能

## 建议的索引
```sql
-- 为临时表创建复合索引以优化查询性能
CREATE INDEX idx_temp_pre_detailed_key ON atr_temp_to_lrc_t_ul_r_pre 
(treaty_no, policy_no, endorse_seq_no, kind_code, sectiono_code, reinsurer_code);

-- 为分区查询创建索引
CREATE INDEX idx_temp_pre_detailed_pn ON atr_temp_to_lrc_t_ul_r_pre (pn);
```

## 后续优化建议
1. **缓存机制**: 可考虑为频繁查询的数据添加本地缓存
2. **批量查询**: 如果发现单条查询性能不佳，可改为批量查询+本地匹配
3. **分区表**: 考虑使用PostgreSQL的分区表特性进一步优化
4. **监控告警**: 添加内存使用和查询性能的监控告警

## 测试验证
建议进行以下测试：
1. **功能测试**: 验证计算结果与优化前完全一致
2. **性能测试**: 对比优化前后的内存使用和执行时间
3. **压力测试**: 在大数据量场景下验证不再出现OOM
4. **并发测试**: 验证多线程环境下的数据一致性
