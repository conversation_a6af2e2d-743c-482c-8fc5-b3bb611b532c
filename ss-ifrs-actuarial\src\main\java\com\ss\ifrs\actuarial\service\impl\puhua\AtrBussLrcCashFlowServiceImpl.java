package com.ss.ifrs.actuarial.service.impl.puhua;

import com.ss.ifrs.actuarial.constant.ActuarialConstant;
import com.ss.ifrs.actuarial.dao.AtrBussLrcTiDao;
import com.ss.ifrs.actuarial.dao.buss.puhua.lrc.*;
import com.ss.ifrs.actuarial.feign.BmsConfCodeFeignClient;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrBussBecfViewVo;
import com.ss.ifrs.actuarial.pojo.puhua.vo.AtrConfBecfOutPutVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.po.AtrBussLrcAction;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussLrcActionVo;
import com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussQuotaValueVo;
import com.ss.ifrs.actuarial.pojo.atrconf.vo.AtrConfBussPeriodVo;
import com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo;
import com.ss.ifrs.actuarial.service.*;
import com.ss.ifrs.actuarial.service.impl.AtrBussLrcService;
import com.ss.ifrs.actuarial.service.puhua.AtrBussPuHuaLrcActionService;
import com.ss.library.excel.ExcelSheet;
import com.ss.library.excel.ExcelSheetData;
import com.ss.library.mybatis.model.Page;
import com.ss.library.mybatis.model.Pageable;
import com.ss.library.utils.FilterUtil;
import com.ss.platform.core.constant.CommonConstant;
import com.ss.platform.core.model.SsException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: AtrBussCalcServiceImpl
 * @Description: 计量计算服务接口实现类
 * @Author: yinxh.
 * @CreateDate: 2021/3/8 17:58
 * @Version: 1.0
 */
@Service(value = "atrBussPuHuaLrcActionService")
public class AtrBussLrcCashFlowServiceImpl implements AtrBussPuHuaLrcActionService {

	// 日志管理
	final Logger LOG  = LoggerFactory.getLogger(getClass());

	@Autowired
    BmsConfCodeFeignClient bmsConfCodeFeignClient;

	@Autowired
	AtrConfCodeService atrConfCodeService;

	@Autowired
	AtrConfModelDefService atrConfModelDefService;

	@Autowired
	private AtrBussPHLrcActionDao atrBussLrcActionDao;

	@Autowired
	private AtrBussDDLrcGDao atrBussDDLrcGDao;
	@Autowired
	private AtrBussDDLrcGDevDao atrBussDDLrcGDevDao;
	@Autowired
	private AtrBussDDLrcUDao atrBussDDLrcUDao;
	@Autowired
	private AtrBussDDLrcUDevDao atrBussDDLrcUDevDao;

	@Autowired
	private AtrBussFOLrcGDao atrBussFOLrcGDao;
	@Autowired
	private AtrBussFOLrcGDevDao atrBussFOLrcGDevDao;
	@Autowired
	private AtrBussFOLrcUDao atrBussFOLrcUDao;
	@Autowired
	private AtrBussFOLrcUDevDao atrBussFOLrcUDevDao;

	@Autowired
	private AtrBussTILrcGDao atrBussTILrcGDao;
	@Autowired
	private AtrBussTILrcGDevDao atrBussTILrcGDevDao;
	@Autowired
	private AtrBussTILrcUDao atrBussTILrcUDao;
	@Autowired
	private AtrBussTILrcUDevDao atrBussTILrcUDevDao;

	@Autowired
	private AtrBussTOLrcGDao atrBussTOLrcGDao;
	@Autowired
	private AtrBussTOLrcGDevDao atrBussTOLrcGDevDao;
	@Autowired
	private AtrBussTOLrcUDao atrBussTOLrcUDao;
	@Autowired
	private AtrBussTOLrcUDevDao atrBussTOLrcUDevDao;

	@Autowired
	AtrExportService atrExportService;

	@Autowired
	AtrBussBecfQuotaService atrBussBecfQuotaService;

	@Resource
	private AtrBussLrcService atrBussLrcService;
    @Autowired
    private AtrBussLrcTiDao atrBussLrcTiDao;

	@Autowired
	private AtrDapTreatyService atrDapTreatyService;

	@Override
	public Page<AtrBussLrcActionVo> findForDataTables(AtrBussLrcActionVo atrBussLicCashFlowVo, Pageable pageParam) {
		return atrBussLrcActionDao.fuzzySearchPage(atrBussLicCashFlowVo, pageParam);
	}

	@Override
	@Async("ruleThreadPool")
	@Transactional(propagation = Propagation.NOT_SUPPORTED, rollbackFor = RuntimeException.class)
	public void save(AtrBussLrcActionVo atrBussLicCashFlowVo, Long userId) {
		Long entityId = atrBussLicCashFlowVo.getEntityId();
		String yearMonth = atrBussLicCashFlowVo.getYearMonth();
		String businessSourceCode = atrBussLicCashFlowVo.getBusinessSourceCode();
		atrBussLrcService.entry(entityId, yearMonth, businessSourceCode);
	}

	@Override
	public AtrBussLrcActionVo findById(Long id) {
		return atrBussLrcActionDao.findByid(id);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public void delete(Long id, Long userId) {
		atrBussLrcActionDao.deleteById(id);
	}


	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public Boolean confirm(AtrBussLrcActionVo atrBussLicCashFlowVo, Long userId) {
		Boolean confirmFlag = true;
		AtrBussLrcAction atrBussLrcAction = new AtrBussLrcAction();
		atrBussLrcAction.setEntityId(atrBussLicCashFlowVo.getEntityId());
		atrBussLrcAction.setYearMonth(atrBussLicCashFlowVo.getYearMonth());
		atrBussLrcAction.setBusinessSourceCode(atrBussLicCashFlowVo.getBusinessSourceCode());
		atrBussLrcAction.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
		Long confirmCount = atrBussLrcActionDao.count(atrBussLrcAction);
		if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount==0) {
			AtrBussLrcAction po = new AtrBussLrcAction();
			Date date = new Date();
			po.setId(atrBussLicCashFlowVo.getId());
			po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
			po.setConfirmUser(userId);
			po.setConfirmTime(date);
			po.setUpdatorId(userId);
			po.setUpdateTime(date);
			atrBussLrcActionDao.updateById(po);

			AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
			atrConfBussPeriodVo.setEntityId(atrBussLicCashFlowVo.getEntityId());
			atrConfBussPeriodVo.setYearMonth(atrBussLicCashFlowVo.getYearMonth());
			atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LRC);
			atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
		} else {
			confirmFlag = false;
		}
		return confirmFlag;
	}

	@Override
	public Page<List<Map<String, Object>>> findPeriodData(AtrDapDrawVo atrDapDrawVo, Pageable pageable) {
		Page<List<Map<String, Object>>> feeTypeList = null;
		if(ObjectUtils.isEmpty(atrDapDrawVo) && ObjectUtils.isEmpty(atrDapDrawVo.getBusinessSourceCode())){
			return feeTypeList;
		}
		AtrBussQuotaValueVo atrBussQuotaValueVo = new AtrBussQuotaValueVo();
		atrBussQuotaValueVo.setBusinessSourceCode(atrDapDrawVo.getBusinessSourceCode());
		List<AtrConfBecfOutPutVo> a = atrBussBecfQuotaService.findBecfOutPut(atrBussQuotaValueVo);
		Optional<AtrConfBecfOutPutVo> optional = a.stream().filter(vo-> atrDapDrawVo.getFeeType().equals(vo.getRemark())).findFirst();
		Boolean isIcg = false;
		Boolean isDev = false;
		Integer startDevNo = 0;
		Integer endDevNo = ActuarialConstant.Export.CF_MAX_COLUMN_DEV;
		if (optional.isPresent()) {
			isIcg = "G".equals(optional.get().getDimension());
			isDev = "1".equals(optional.get().getType());
			if (ObjectUtils.isNotEmpty(optional.get().getStartDevNo())) {
				startDevNo = optional.get().getStartDevNo();
			}
			if (ObjectUtils.isNotEmpty(optional.get().getEndDevNo())) {
				endDevNo = optional.get().getEndDevNo();
			}
			atrDapDrawVo.setFeeType(optional.get().getOutCode());
		} else {
			return feeTypeList;
		}
		if (isDev) {
			Integer finalStartDevNo = startDevNo;
			Integer finalEndDevNo = endDevNo;
			if (isIcg) {
				List<Integer> icgDevNoList = this.findIcgDevNo(atrDapDrawVo);
				atrDapDrawVo.setDevNoList(icgDevNoList.stream()
						.filter(devNo -> devNo>= finalStartDevNo && devNo<= finalEndDevNo)
						.collect(Collectors.toList()));
			} else {
				List<Integer> icuDevNoList = this.findPeriodHeaderData(atrDapDrawVo);
				atrDapDrawVo.setDevNoList(icuDevNoList.stream()
						.filter(devNo -> devNo>= finalStartDevNo && devNo<= finalEndDevNo)
						.collect(Collectors.toList()));
			}
		}
		switch (atrDapDrawVo.getBusinessSourceCode()) {
			case "DD":
				if (isIcg) {
					feeTypeList= atrBussDDLrcGDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussDDLrcUDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "FO":
				if (isIcg) {
					feeTypeList= atrBussFOLrcGDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussFOLrcUDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TI":
				if (isIcg) {
					feeTypeList= atrBussTILrcGDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussTILrcUDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TO":
				if (isIcg) {
					feeTypeList= atrBussTOLrcGDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussTOLrcUDao.findTotLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TX":
				feeTypeList = atrBussTOLrcUDao.findToxLrcDetailPage(atrDapDrawVo, pageable);
				break;
			default:
				break;
		}
		return feeTypeList;
	}

	/*
	 * 查询发展期List
	 * */
	@Override
	public List<Integer> findPeriodHeaderData(AtrDapDrawVo atrDapDrawVo) {
		List<Integer> icuDevNoList = new ArrayList<>();
		switch (atrDapDrawVo.getBusinessSourceCode()) {
			case "DD":
				icuDevNoList = atrBussDDLrcUDevDao.findByVo(atrDapDrawVo);
				break;
			case "FO":
				icuDevNoList = atrBussFOLrcUDevDao.findByVo(atrDapDrawVo);
				break;
			case "TI":
				icuDevNoList = atrBussTILrcUDevDao.findByVo(atrDapDrawVo);
				break;
			case "TO":
				icuDevNoList = atrBussTOLrcUDevDao.findByVo(atrDapDrawVo);
				break;
			case "TX":
				icuDevNoList = atrBussTOLrcUDevDao.findxByVo(atrDapDrawVo);
				break;
		};
		return icuDevNoList;
	}

	public List<Integer> findIcgDevNo(AtrDapDrawVo atrDapDrawVo) {
		List<Integer> icuDevNoList = new ArrayList<>();
		switch (atrDapDrawVo.getBusinessSourceCode()) {
			case "DD":
				icuDevNoList = atrBussDDLrcGDevDao.findByVo(atrDapDrawVo);
				break;
			case "FO":
				icuDevNoList = atrBussFOLrcGDevDao.findByVo(atrDapDrawVo);
				break;
			case "TI":
				icuDevNoList = atrBussTILrcGDevDao.findByVo(atrDapDrawVo);
				break;
			case "TO":
				icuDevNoList = atrBussTOLrcGDevDao.findByVo(atrDapDrawVo);
				break;
		};
		return icuDevNoList;
	}


	@Override
	@Async("ruleThreadPool")
	public void download1(AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		AtrBussLrcAction po = atrBussLrcActionDao.findById(atrBussLrcActionVo.getId());
		if (ObjectUtils.isNotEmpty(po)) {
			String zipName = atrExportService.getZipName(atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getLogFileName());
			String logPath = atrExportService.getOutPutPathSave() + zipName;;
			Long logId = atrExportService.saveExportTrackLog(zipName, atrBussLrcActionVo.getTargetRouter(), logPath, atrBussLrcActionVo.getCreatorId(), false);
			boolean taskStatus = true;
			String errorMsg = null;
			atrBussLrcActionVo.setZipName(zipName);
			atrBussLrcActionVo.setBusinessSourceCode(atrBussLrcActionVo.getBusinessSourceCode()!=null
                    ?atrBussLrcActionVo.getBusinessSourceCode():po.getBusinessSourceCode());
			try{
				this.downloadData(po.getActionNo(), atrBussLrcActionVo, request, response);
			} catch (Exception e) {
				taskStatus = false;
				errorMsg = ExceptionUtils.getStackTrace(e);
				if (errorMsg.length() >= 3900) {
					errorMsg = errorMsg.substring(1, 3900);
				}
				LOG.info(errorMsg);
			} finally {
				atrExportService.updateExportTrackLog(logId,
						taskStatus ? CommonConstant.ExportLogTaskStatus.SUCCESSFUL : CommonConstant.ExportLogTaskStatus.FAIL
						, atrBussLrcActionVo.getCreatorId(), errorMsg);
			}

		}
	}

	@Override
	public Page<AtrDapDrawVo> findPortfolioData(AtrDapDrawVo atrDapDrawVo, Pageable pageParam) {
		if (atrDapDrawVo == null || atrDapDrawVo.getId() == null) {
			return (Page<AtrDapDrawVo>) CollectionUtils.EMPTY_COLLECTION;
		}
		AtrBussLrcActionVo atrBussLrcActionVo = this.findById(atrDapDrawVo.getId());
		Page<AtrDapDrawVo> result = null;
		if(ObjectUtils.isNotEmpty(atrBussLrcActionVo)&& ObjectUtils.isNotEmpty(atrBussLrcActionVo.getActionNo()))
		{
			atrBussLrcActionVo.setPortfolioNo(FilterUtil.transitionSearch(atrDapDrawVo.getPortfolioNo()));
			atrBussLrcActionVo.setIcgNo(FilterUtil.transitionSearch(atrDapDrawVo.getIcgNo()));
			atrBussLrcActionVo.setRiskClassCode(atrDapDrawVo.getRiskClassCode());
			result = atrBussLrcActionDao.findPortfolioData(atrBussLrcActionVo, pageParam);
		}
		return result;
	}

	@Override
	public void calculateAll(AtrBussLrcActionVo lrcActionVo, Long userId) {
		Long entityId = lrcActionVo.getEntityId();
		String yearMonth = lrcActionVo.getYearMonth();
		atrBussLrcService.entry(entityId, yearMonth, "DD");
		atrBussLrcService.entry(entityId, yearMonth, "FO");
		atrBussLrcService.entry(entityId, yearMonth, "TI");
		atrBussLrcService.entry(entityId, yearMonth, "TO");
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = RuntimeException.class)
	public String confirmLrcCfVersion(AtrBussLrcActionVo lrcActionVo, Long userId) {
		List<String> businessModelList = bmsConfCodeFeignClient.findByCodeIdx("BusinessModel/Base");
		//po.setCurrencyCode(lrcActionVo.getCurrencyCode());
		if (ObjectUtils.isEmpty(businessModelList)) {
			return "not business model";
		}
		businessModelList.forEach(businessModel-> {
			AtrBussLrcAction po = new AtrBussLrcAction();
			po.setEntityId(lrcActionVo.getEntityId());
			po.setYearMonth(lrcActionVo.getYearMonth());
			po.setBusinessSourceCode(businessModel);
			po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
			Long confirmCount = atrBussLrcActionDao.count(po);
			po.setConfirmIs(CommonConstant.VersionStatus.PENDING);
			Long unConfirmCount = atrBussLrcActionDao.count(po);
			if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount == 0 && ObjectUtils.isNotEmpty(unConfirmCount) && unConfirmCount > 0) {
				Date date = new Date();
				po.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
				po.setConfirmUser(userId);
				po.setConfirmTime(date);
				atrBussLrcActionDao.updateConfirm(po);
			}
		});
		AtrConfBussPeriodVo atrConfBussPeriodVo = new AtrConfBussPeriodVo();
		atrConfBussPeriodVo.setEntityId(lrcActionVo.getEntityId());
		atrConfBussPeriodVo.setYearMonth(lrcActionVo.getYearMonth());
		atrConfBussPeriodVo.setProcCode(ActuarialConstant.ProcCode.EXPECTED_CF_LRC);
		atrConfModelDefService.confirmBeCFProc(atrConfBussPeriodVo, userId);
		return null;
	}

	@Override
	public void revoke(AtrBussLrcActionVo bussLrcActionVo, Long userId) {
		AtrBussLrcAction bussLrcAction = new AtrBussLrcAction();
		bussLrcAction.setEntityId(bussLrcActionVo.getEntityId());
		bussLrcAction.setYearMonth(bussLrcActionVo.getYearMonth());
		bussLrcAction.setBusinessSourceCode(bussLrcActionVo.getBusinessSourceCode());
		bussLrcAction.setConfirmIs(CommonConstant.VersionStatus.CONFIRMED);
		Long confirmCount = atrBussLrcActionDao.count(bussLrcAction);
		if (ObjectUtils.isNotEmpty(confirmCount) && confirmCount > 0) {
			Date date = new Date();
			bussLrcAction.setConfirmIs(CommonConstant.VersionStatus.PENDING);
			bussLrcAction.setConfirmUser(userId);
			bussLrcAction.setConfirmTime(date);
			bussLrcAction.setUpdatorId(userId);
			bussLrcAction.setUpdateTime(date);
			atrBussLrcActionDao.revoke(bussLrcAction);
		}
	}

	@Override
	public void becfDownload(AtrBussBecfViewVo atrBussBecfViewVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		AtrBussLrcAction po = atrBussLrcActionDao.findById(atrBussBecfViewVo.getId());
		if (ObjectUtils.isNotEmpty(po)) {
			String zipName = atrExportService.getZipName(atrBussBecfViewVo.getLrcTemplateFileName(), atrBussBecfViewVo.getLogFileName());
			String logPath = atrExportService.getOutPutPathSave() + zipName;;
			Long logId = atrExportService.saveExportTrackLog(zipName, atrBussBecfViewVo.getTargetRouter(), logPath, atrBussBecfViewVo.getCreatorId(), false);
			boolean taskStatus = true;
			String errorMsg = null;
			atrBussBecfViewVo.setZipName(zipName);
			atrBussBecfViewVo.setBusinessSourceCode(po.getBusinessSourceCode());
			try{
				//this.downloadData(po.getActionNo(), atrBussBecfViewVo, request, response);
			} catch (Exception e) {
				taskStatus = false;
				errorMsg = ExceptionUtils.getStackTrace(e);
				if (errorMsg.length() >= 3900) {
					errorMsg = errorMsg.substring(1, 3900);
				}
				LOG.info(errorMsg);
			} finally {
				atrExportService.updateExportTrackLog(logId,
						taskStatus ? CommonConstant.ExportLogTaskStatus.SUCCESSFUL : CommonConstant.ExportLogTaskStatus.FAIL
						, atrBussBecfViewVo.getCreatorId(), errorMsg);
			}

		}
	}

	private void downloadData(String actionNo, AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		// 保存原始的参数
		String originalBusinessSourceCode = atrBussLrcActionVo.getBusinessSourceCode();
		String originalTemplateFileName = atrBussLrcActionVo.getTemplateFileName();
		String originalLogFileName = atrBussLrcActionVo.getLogFileName();

		// 创建一个统一的文件集合，用于存储所有需要打包的文件
		Map<String, String> allFiles = new HashMap<>();

		// 处理合约分出模板导出
		Map<String, String> toFiles = processDownloadFiles(actionNo, atrBussLrcActionVo, request, response);
		if (ObjectUtils.isNotEmpty(toFiles)) {
			allFiles.putAll(toFiles);
		}

		// 如果是TO类型且需要额外导出超赔分出模板
		if ("TO".equals(originalBusinessSourceCode) && Boolean.TRUE.equals(atrBussLrcActionVo.getNeedExportTX())) {
			// 恢复原始参数
			atrBussLrcActionVo.setBusinessSourceCode(originalBusinessSourceCode);

			// 设置超赔分出模板相关参数
			atrBussLrcActionVo.setBusinessSourceCode("TX");
			atrBussLrcActionVo.setTemplateFileName(atrBussLrcActionVo.getTxTemplateName());
			// 修改日志文件名，添加TX后缀以区分
			String txLogFileName = originalLogFileName + "_TX";
			atrBussLrcActionVo.setLogFileName(txLogFileName);

			// 处理超赔分出模板导出，但不单独打包
			Map<String, String> txFiles = processDownloadFiles(actionNo, atrBussLrcActionVo, request, response);
			if (ObjectUtils.isNotEmpty(txFiles)) {
				allFiles.putAll(txFiles);
			}

			// 恢复原始参数
			atrBussLrcActionVo.setBusinessSourceCode(originalBusinessSourceCode);
			atrBussLrcActionVo.setTemplateFileName(originalTemplateFileName);
			atrBussLrcActionVo.setLogFileName(originalLogFileName);
		}

		// 将所有文件打包成一个ZIP
		if (ObjectUtils.isNotEmpty(allFiles)) {
			String zipName = atrExportService.getZipName(atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getLogFileName());
			atrBussLrcActionVo.setZipName(zipName);
			atrExportService.dealZip(zipName, allFiles, atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
		}
	}

	// 抽取的处理下载逻辑，返回生成的文件集合而不是直接打包
	private Map<String, String> processDownloadFiles(String actionNo, AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		AtrBussQuotaValueVo atrBussQuotaValueVo = new AtrBussQuotaValueVo();
		atrBussQuotaValueVo.setBusinessSourceCode(atrBussLrcActionVo.getBusinessSourceCode());
		atrBussQuotaValueVo.setBecfType("Lrc");
		List<AtrConfBecfOutPutVo> becfOutPuts = atrBussBecfQuotaService.findBecfOutPut(atrBussQuotaValueVo);

		AtrDapDrawVo atrDapDrawVo = new AtrDapDrawVo();
		atrDapDrawVo.setActionNo(actionNo);
		atrDapDrawVo.setPortfolioNo(atrBussLrcActionVo.getPortfolioNo());
		atrDapDrawVo.setIcgNo(atrBussLrcActionVo.getIcgNo());
		atrDapDrawVo.setRiskClassCode(atrBussLrcActionVo.getRiskClassCode());
		atrDapDrawVo.setBusinessSourceCode(atrBussLrcActionVo.getBusinessSourceCode());
		atrDapDrawVo.setLanguage(atrBussLrcActionVo.getLanguage());
		List<Integer> icuDevNoList = this.findPeriodHeaderData(atrDapDrawVo);
		List<Integer> icgDevNoList = this.findIcgDevNo(atrDapDrawVo);
		Long countU = atrBussDDLrcUDao.countDateByVo(atrDapDrawVo);
		Map<String, String> files = new HashMap<>();
		for (int i = 0; i<= countU/ActuarialConstant.Export.CF_MAX_ROW ; i++) {
			List<ExcelSheet> sheetList = new ArrayList<>();
			atrDapDrawVo.setLimit(ActuarialConstant.Export.CF_MAX_ROW);
			atrDapDrawVo.setOffset(i * ActuarialConstant.Export.CF_MAX_ROW);
			Pageable pageable = new Pageable(i, Math.toIntExact(ActuarialConstant.Export.CF_MAX_ROW));

			for(int j = 0; j< becfOutPuts.size(); j++){
				ExcelSheet excelSheet = genExcelSheet(j, becfOutPuts.get(j), atrDapDrawVo, pageable, icuDevNoList, icgDevNoList);
				sheetList.add(excelSheet);
			}
			List<Class> voClassList = new ArrayList<>();
			LOG.info("LRC导出数据查询结束时间：" + LocalTime.now());
			Map<String, String> excelName = atrExportService.syncExportSheetList(null, sheetList, voClassList,
					atrBussLrcActionVo.getTemplateFileName(), atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
			files.putAll(excelName);
		}
		return files;
	}

	// 旧的processDownload方法现在被替换，为保持兼容性，保留此方法但标记为过时
	@Deprecated
	private void processDownload(String actionNo, AtrBussLrcActionVo atrBussLrcActionVo, HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, String> files = processDownloadFiles(actionNo, atrBussLrcActionVo, request, response);
		if (ObjectUtils.isNotEmpty(files)) {
			atrExportService.dealZip(atrBussLrcActionVo.getZipName(), files, atrBussLrcActionVo.getTargetRouter(), atrBussLrcActionVo.getCreatorId());
		}
	}

	private ExcelSheet genExcelSheet(int sheetNo, AtrConfBecfOutPutVo becfOutPutVo, AtrDapDrawVo atrDapDrawVo, Pageable pageable, List<Integer> icuDevNoList, List<Integer> icgDevNoList) {
		atrDapDrawVo.setFeeType(becfOutPutVo.getOutCode());
		Page<List<Map<String, Object>>> detailListPage = this.findPeriodData1(becfOutPutVo, atrDapDrawVo, pageable, icuDevNoList, icgDevNoList);
		return getSheet(sheetNo, becfOutPutVo.getRemark(), detailListPage.getContent(), null);
	}

 	public Page<List<Map<String, Object>>> findPeriodData1(AtrConfBecfOutPutVo becfOutPutVo, AtrDapDrawVo atrDapDrawVo, Pageable pageable, List<Integer> icuDevNoList, List<Integer> icgDevNoList) {
		Page<List<Map<String, Object>>> feeTypeList = null;
		if(ObjectUtils.isEmpty(atrDapDrawVo) && ObjectUtils.isEmpty(atrDapDrawVo.getBusinessSourceCode())){
			return feeTypeList;
		}
 	    Boolean isIcg = "G".equals(becfOutPutVo.getDimension());
		Boolean isDev = "1".equals(becfOutPutVo.getType());
		atrDapDrawVo.setDevNoList(Collections.emptyList());
		if (isIcg && isDev) {
			atrDapDrawVo.setDevNoList(icgDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));
		}
		if (!isIcg && isDev) {
			atrDapDrawVo.setDevNoList(icuDevNoList.stream().filter(devNo -> devNo<= ActuarialConstant.Export.CF_MAX_COLUMN_DEV).collect(Collectors.toList()));
		}
		switch (atrDapDrawVo.getBusinessSourceCode()) {
			case "DD":
				if (isIcg) {
					feeTypeList= atrBussDDLrcGDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussDDLrcUDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "FO":
				if (isIcg) {
					feeTypeList= atrBussFOLrcGDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussFOLrcUDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TI":
				if (isIcg) {
					feeTypeList= atrBussTILrcGDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussTILrcUDao.findLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TO":
				if (isIcg) {
					feeTypeList= atrBussTOLrcGDao.findLrcIcgDetail(atrDapDrawVo, pageable);
				} else {
					feeTypeList= atrBussTOLrcUDao.findTotLrcDetailPage(atrDapDrawVo, pageable);
				}
				break;
			case "TX":
				feeTypeList = atrBussTOLrcUDao.findToxLrcDetailPage(atrDapDrawVo, pageable);
				break;
			default:
				break;
		}
		return feeTypeList;
	}



	//组装Excel的Sheet
	private ExcelSheet getSheet(int sheetNo,String sheetName, List<?> beanData, Class voClazz) {
		ExcelSheet sheet = new ExcelSheet();
		sheet.setSheetNo(sheetNo);
		sheet.setSheetName(sheetName);
		List<ExcelSheetData> sheetDataList = new ArrayList<>();
		ExcelSheetData sheetData = new ExcelSheetData();
		sheetData.setBeanKey("df");
		sheetData.setBeanData(beanData);
		//sheetData.setVoClazz(voClazz);
		sheetDataList.add(sheetData);
		sheet.setSheetDataList(sheetDataList);
		return sheet;
	}

	//组装Excel的Sheet
	private ExcelSheet getSheet1(int sheetNo,String sheetName, List<?> beanData, List<?> beanData2) {
		ExcelSheet sheet = new ExcelSheet();
		sheet.setSheetNo(sheetNo);
		sheet.setSheetName(sheetName);
		List<ExcelSheetData> sheetDataList = new ArrayList<>();
		ExcelSheetData sheetData = new ExcelSheetData();
		sheetData.setBeanKey("df");
		sheetData.setBeanData(beanData2);

		ExcelSheetData sheetData2 = new ExcelSheetData();
		sheetData.setBeanKey("dp");
		sheetData.setBeanData(beanData);
		//sheetData.setVoClazz(voClazz);
		sheetDataList.add(sheetData);
		sheetDataList.add(sheetData2);
		sheet.setSheetDataList(sheetDataList);
		return sheet;
	}
	/**
	 * List<Map>对象复制功能
	 * List<Map> 转化为 List<Map>
	 * @param list
	 */
	public static List<Map<String, Object>> convert(List<Map<String, Object>> list) {
		List<Map<String, Object>> newMapList = new ArrayList<>();
		for (Map map : list) {
			try {
				newMapList.add((Map) SerializationUtils.clone((Serializable) map));
			} catch (Exception ex) {
				throw new SsException(ex.getLocalizedMessage(), ex);
			}
		}
		return newMapList;
	}
}
